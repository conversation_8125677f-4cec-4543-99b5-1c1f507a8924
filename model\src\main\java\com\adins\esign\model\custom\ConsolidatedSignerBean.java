package com.adins.esign.model.custom;

import java.util.List;

/**
 * Consolidated signer bean for handling multiple sign locations per signer
 * Used specifically for Privy vendor to consolidate duplicate signers
 */
public class ConsolidatedSignerBean extends SignerBean {
	private static final long serialVersionUID = 1L;
	
	private List<SignLocationTemplateBean> signLocations;
	private boolean isConsolidated;
	
	public ConsolidatedSignerBean() {
		super();
		this.isConsolidated = false;
	}
	
	public ConsolidatedSignerBean(SignerBean originalSigner) {
		super();
		// Copy all properties from original signer
		this.setSignAction(originalSigner.getSignAction());
		this.setSignerType(originalSigner.getSignerType());
		this.setSignSequence(originalSigner.getSignSequence());
		this.setSignerSelfPhoto(originalSigner.getSignerSelfPhoto());
		this.setIsRegistered(originalSigner.getIsRegistered());
		this.setIsActive(originalSigner.getIsActive());
		
		// Copy UserBean properties
		this.setIdNo(originalSigner.getIdNo());
		this.setUserName(originalSigner.getUserName());
		this.setEmail(originalSigner.getEmail());
		this.setUserPhone(originalSigner.getUserPhone());
		this.setUserAddress(originalSigner.getUserAddress());
		this.setUserDob(originalSigner.getUserDob());
		this.setUserPob(originalSigner.getUserPob());
		this.setUserGender(originalSigner.getUserGender());
		this.setIdPhoto(originalSigner.getIdPhoto());
		this.setSelfPhoto(originalSigner.getSelfPhoto());
		this.setUserPob(originalSigner.getUserPob());
		
		this.isConsolidated = false;
	}
	
	public List<SignLocationTemplateBean> getSignLocations() {
		return signLocations;
	}
	
	public void setSignLocations(List<SignLocationTemplateBean> signLocations) {
		this.signLocations = signLocations;
	}
	
	public boolean isConsolidated() {
		return isConsolidated;
	}
	
	public void setConsolidated(boolean isConsolidated) {
		this.isConsolidated = isConsolidated;
	}
}
